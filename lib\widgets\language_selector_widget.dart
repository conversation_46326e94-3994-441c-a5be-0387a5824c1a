import 'package:flutter/material.dart';
import '../services/localization_service.dart';

/// Widget for selecting app language
class LanguageSelectorWidget extends StatefulWidget {
  final Function(String)? onLanguageChanged;
  
  const LanguageSelectorWidget({
    super.key,
    this.onLanguageChanged,
  });

  @override
  State<LanguageSelectorWidget> createState() => _LanguageSelectorWidgetState();
}

class _LanguageSelectorWidgetState extends State<LanguageSelectorWidget> {
  final LocalizationService _localizationService = LocalizationService();
  String _selectedLanguage = 'en';

  @override
  void initState() {
    super.initState();
    _selectedLanguage = _localizationService.currentLocale.languageCode;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.language,
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Language / اللغة / زمان',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.titleLarge?.color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ..._buildLanguageOptions(),
        ],
      ),
    );
  }

  List<Widget> _buildLanguageOptions() {
    final languages = _localizationService.getAvailableLanguages();
    
    return languages.map((language) {
      final languageCode = language['code']!;
      final languageName = language['name']!;
      final isRTL = language['isRTL'] == 'true';
      final isSelected = languageCode == _selectedLanguage;
      
      return Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: InkWell(
          onTap: () => _selectLanguage(languageCode),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected 
                  ? Theme.of(context).primaryColor.withOpacity(0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected 
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).dividerColor,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                // Language flag or icon
                Container(
                  width: 32,
                  height: 24,
                  decoration: BoxDecoration(
                    color: _getLanguageColor(languageCode),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Center(
                    child: Text(
                      _getLanguageFlag(languageCode),
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                
                // Language name
                Expanded(
                  child: Text(
                    languageName,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      color: isSelected 
                          ? Theme.of(context).primaryColor
                          : Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                    textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
                  ),
                ),
                
                // RTL indicator
                if (isRTL)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'RTL',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                
                // Selection indicator
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      );
    }).toList();
  }

  String _getLanguageFlag(String languageCode) {
    switch (languageCode) {
      case 'en':
        return '🇺🇸';
      case 'ar':
        return '🇮🇶';
      case 'ku':
        return '🏴';
      default:
        return '🌐';
    }
  }

  Color _getLanguageColor(String languageCode) {
    switch (languageCode) {
      case 'en':
        return Colors.blue.withOpacity(0.1);
      case 'ar':
        return Colors.green.withOpacity(0.1);
      case 'ku':
        return Colors.orange.withOpacity(0.1);
      default:
        return Colors.grey.withOpacity(0.1);
    }
  }

  void _selectLanguage(String languageCode) async {
    if (languageCode == _selectedLanguage) return;
    
    setState(() {
      _selectedLanguage = languageCode;
    });
    
    // Change language
    await _localizationService.changeLanguage(languageCode);
    
    // Notify parent widget
    widget.onLanguageChanged?.call(languageCode);
    
    // Show confirmation
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _getLanguageChangeMessage(languageCode),
            textDirection: _localizationService.textDirection,
          ),
          duration: const Duration(seconds: 2),
          backgroundColor: Theme.of(context).primaryColor,
        ),
      );
    }
  }

  String _getLanguageChangeMessage(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Language changed to English';
      case 'ar':
        return 'تم تغيير اللغة إلى العربية';
      case 'ku':
        return 'زمان گۆڕدرا بۆ کوردی';
      default:
        return 'Language changed';
    }
  }
}

/// Simple language dropdown for compact spaces
class LanguageDropdownWidget extends StatefulWidget {
  final Function(String)? onLanguageChanged;
  
  const LanguageDropdownWidget({
    super.key,
    this.onLanguageChanged,
  });

  @override
  State<LanguageDropdownWidget> createState() => _LanguageDropdownWidgetState();
}

class _LanguageDropdownWidgetState extends State<LanguageDropdownWidget> {
  final LocalizationService _localizationService = LocalizationService();
  String _selectedLanguage = 'en';

  @override
  void initState() {
    super.initState();
    _selectedLanguage = _localizationService.currentLocale.languageCode;
  }

  @override
  Widget build(BuildContext context) {
    final languages = _localizationService.getAvailableLanguages();
    
    return DropdownButton<String>(
      value: _selectedLanguage,
      icon: const Icon(Icons.arrow_drop_down),
      iconSize: 24,
      elevation: 16,
      style: TextStyle(
        color: Theme.of(context).textTheme.bodyLarge?.color,
        fontSize: 16,
      ),
      underline: Container(
        height: 2,
        color: Theme.of(context).primaryColor,
      ),
      onChanged: (String? newValue) {
        if (newValue != null && newValue != _selectedLanguage) {
          _selectLanguage(newValue);
        }
      },
      items: languages.map<DropdownMenuItem<String>>((language) {
        final languageCode = language['code']!;
        final languageName = language['name']!;
        
        return DropdownMenuItem<String>(
          value: languageCode,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(_getLanguageFlag(languageCode)),
              const SizedBox(width: 8),
              Text(languageName),
            ],
          ),
        );
      }).toList(),
    );
  }

  String _getLanguageFlag(String languageCode) {
    switch (languageCode) {
      case 'en':
        return '🇺🇸';
      case 'ar':
        return '🇮🇶';
      case 'ku':
        return '🏴';
      default:
        return '🌐';
    }
  }

  void _selectLanguage(String languageCode) async {
    setState(() {
      _selectedLanguage = languageCode;
    });
    
    await _localizationService.changeLanguage(languageCode);
    widget.onLanguageChanged?.call(languageCode);
  }
}
